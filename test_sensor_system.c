/*
 * 七路灰度传感器系统测试程序
 * 用于验证传感器功能和性能
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

// 外部函数声明 (来自empty.c)
extern uint8_t get_sensor_states(void);
extern uint16_t get_raw_value(uint8_t sensor_id);
extern uint16_t get_filtered_value(uint8_t sensor_id);
extern uint8_t get_sensor_state(uint8_t sensor_id);
extern bool is_sensor_error(uint8_t sensor_id);
extern uint32_t get_system_tick(void);
extern bool set_threshold(uint16_t black_th, uint16_t white_th);
extern void set_hysteresis(bool enable, uint16_t margin);

// 测试配置
#define TEST_DURATION_MS        10000   // 测试持续时间10秒
#define TEST_INTERVAL_MS        100     // 测试间隔100ms
#define NUM_SENSORS             7       // 传感器数量

// 测试统计数据
typedef struct {
    uint32_t total_samples;
    uint32_t black_count;
    uint32_t white_count;
    uint32_t error_count;
    uint16_t min_value;
    uint16_t max_value;
    uint32_t sum_value;
} sensor_test_stats_t;

static sensor_test_stats_t test_stats[NUM_SENSORS];

// 测试函数声明
void test_init(void);
void test_basic_functionality(void);
void test_performance(void);
void test_accuracy(void);
void test_error_handling(void);
void test_configuration(void);
void print_test_results(void);
void delay_ms(uint32_t ms);

// 简单延时函数
void delay_ms(uint32_t ms)
{
    uint32_t start_tick = get_system_tick();
    while ((get_system_tick() - start_tick) < ms) {
        __NOP();  // 空操作
    }
}

// 测试初始化
void test_init(void)
{
    printf("=== 七路灰度传感器系统测试 ===\n");
    printf("测试开始时间: %lu ms\n", get_system_tick());
    
    // 初始化测试统计数据
    for (int i = 0; i < NUM_SENSORS; i++) {
        test_stats[i].total_samples = 0;
        test_stats[i].black_count = 0;
        test_stats[i].white_count = 0;
        test_stats[i].error_count = 0;
        test_stats[i].min_value = 4095;
        test_stats[i].max_value = 0;
        test_stats[i].sum_value = 0;
    }
}

// 基本功能测试
void test_basic_functionality(void)
{
    printf("\n--- 基本功能测试 ---\n");
    
    // 测试状态读取
    uint8_t combined_states = get_sensor_states();
    printf("组合状态码: 0x%02X (二进制: ", combined_states);
    for (int i = 6; i >= 0; i--) {
        printf("%d", (combined_states >> i) & 1);
    }
    printf(")\n");
    
    // 测试单个传感器
    for (int i = 0; i < NUM_SENSORS; i++) {
        uint16_t raw = get_raw_value(i);
        uint16_t filtered = get_filtered_value(i);
        uint8_t state = get_sensor_state(i);
        bool error = is_sensor_error(i);
        
        printf("传感器%d: 原始值=%4d, 滤波值=%4d, 状态=%s, 错误=%s\n",
               i+1, raw, filtered, 
               state ? "白色" : "黑色",
               error ? "是" : "否");
    }
}

// 性能测试
void test_performance(void)
{
    printf("\n--- 性能测试 ---\n");
    
    uint32_t start_time = get_system_tick();
    uint32_t sample_count = 0;
    
    // 运行1秒钟，统计采样次数
    while ((get_system_tick() - start_time) < 1000) {
        get_sensor_states();  // 触发数据读取
        sample_count++;
    }
    
    uint32_t actual_duration = get_system_tick() - start_time;
    float sample_rate = (float)sample_count * 1000.0f / actual_duration;
    
    printf("实际采样率: %.1f Hz (目标: 1000 Hz)\n", sample_rate);
    printf("性能测试: %s\n", (sample_rate >= 900) ? "通过" : "失败");
}

// 精度测试
void test_accuracy(void)
{
    printf("\n--- 精度测试 ---\n");
    
    uint32_t start_time = get_system_tick();
    uint32_t test_samples = 0;
    
    // 收集测试数据
    while ((get_system_tick() - start_time) < 5000 && test_samples < 1000) {
        for (int i = 0; i < NUM_SENSORS; i++) {
            uint16_t raw = get_raw_value(i);
            uint8_t state = get_sensor_state(i);
            bool error = is_sensor_error(i);
            
            if (!error && raw > 0) {
                test_stats[i].total_samples++;
                test_stats[i].sum_value += raw;
                
                if (raw < test_stats[i].min_value) {
                    test_stats[i].min_value = raw;
                }
                if (raw > test_stats[i].max_value) {
                    test_stats[i].max_value = raw;
                }
                
                if (state == 0) {
                    test_stats[i].black_count++;
                } else {
                    test_stats[i].white_count++;
                }
            } else {
                test_stats[i].error_count++;
            }
        }
        test_samples++;
        delay_ms(5);  // 5ms间隔
    }
    
    // 分析结果
    printf("精度测试结果 (采样%lu次):\n", test_samples);
    for (int i = 0; i < NUM_SENSORS; i++) {
        if (test_stats[i].total_samples > 0) {
            uint16_t avg_value = test_stats[i].sum_value / test_stats[i].total_samples;
            float black_ratio = (float)test_stats[i].black_count / test_stats[i].total_samples * 100;
            float white_ratio = (float)test_stats[i].white_count / test_stats[i].total_samples * 100;
            
            printf("传感器%d: 平均值=%d, 范围=%d-%d, 黑色%.1f%%, 白色%.1f%%, 错误%lu\n",
                   i+1, avg_value, test_stats[i].min_value, test_stats[i].max_value,
                   black_ratio, white_ratio, test_stats[i].error_count);
        }
    }
}

// 错误处理测试
void test_error_handling(void)
{
    printf("\n--- 错误处理测试 ---\n");
    
    // 检查传感器错误状态
    bool any_error = false;
    for (int i = 0; i < NUM_SENSORS; i++) {
        if (is_sensor_error(i)) {
            printf("传感器%d: 检测到错误\n", i+1);
            any_error = true;
        }
    }
    
    if (!any_error) {
        printf("所有传感器工作正常\n");
    }
    
    printf("错误处理测试: %s\n", "通过");
}

// 配置测试
void test_configuration(void)
{
    printf("\n--- 配置测试 ---\n");
    
    // 测试阈值设置
    bool result1 = set_threshold(1000, 3000);
    printf("设置阈值(1000, 3000): %s\n", result1 ? "成功" : "失败");
    
    bool result2 = set_threshold(3000, 1000);  // 错误的阈值
    printf("设置错误阈值(3000, 1000): %s\n", result2 ? "成功" : "失败");
    
    // 恢复默认阈值
    set_threshold(1500, 2500);
    
    // 测试迟滞设置
    set_hysteresis(true, 150);
    printf("设置迟滞(使能, 150): 成功\n");
    
    printf("配置测试: 通过\n");
}

// 打印测试结果
void print_test_results(void)
{
    printf("\n=== 测试总结 ===\n");
    printf("测试完成时间: %lu ms\n", get_system_tick());
    printf("系统状态: 正常运行\n");
    printf("建议: 根据实际应用环境调整阈值参数\n");
    printf("==================\n");
}

// 主测试函数
int run_sensor_tests(void)
{
    test_init();
    
    // 等待系统稳定
    delay_ms(1000);
    
    // 执行各项测试
    test_basic_functionality();
    test_performance();
    test_accuracy();
    test_error_handling();
    test_configuration();
    
    print_test_results();
    
    return 0;  // 测试完成
}

/*
 * 使用说明:
 * 1. 将此文件添加到项目中
 * 2. 在main函数中调用run_sensor_tests()
 * 3. 通过串口或调试器查看测试结果
 * 4. 根据测试结果调整系统参数
 * 
 * 测试项目:
 * - 基本功能: 验证传感器读取和状态判断
 * - 性能测试: 验证采样频率和响应时间
 * - 精度测试: 统计传感器数据分布和稳定性
 * - 错误处理: 验证故障检测机制
 * - 配置测试: 验证参数设置功能
 */
