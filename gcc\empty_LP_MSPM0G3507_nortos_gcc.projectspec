<?xml version="1.0" encoding="UTF-8"?>
<projectSpec>
    <applicability>
        <when>
            <context
                deviceFamily="ARM"
                deviceId="MSPM0G3507"
            />
        </when>
    </applicability>

    <project
        title="empty"
        name="empty_LP_MSPM0G3507_nortos_gcc"
        configurations="Debug"
        toolChain="GNU"
        connection="TIXDS110_Connection.xml"
        device="MSPM0G3507"
        ignoreDefaultDeviceSettings="true"
        ignoreDefaultCCSSettings="true"
        products="MSPM0-SDK;sysconfig"
        compilerBuildOptions="
            -I${PROJECT_ROOT}
            -I${PROJECT_ROOT}/${ConfigName}
            -O2
            @device.opt
            -I${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include
            -I${COM_TI_MSPM0_SDK_INSTALL_DIR}/source
            -mcpu=cortex-m0plus
            -march=armv6-m
            -mthumb
            -std=c99
            -std=c++11
            -mfloat-abi=soft
            -ffunction-sections
            -fdata-sections
            -g
            -gstrict-dwarf
            -Wall
            -I${CG_TOOL_ROOT}/arm-none-eabi/include/newlib-nano
            -I${CG_TOOL_ROOT}/arm-none-eabi/include
        "
        linkerBuildOptions="
            -L${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/lib/gcc/m0p/mspm0g1x0x_g3x0x
            -nostartfiles
            -Tdevice.lds.genlibs
            -l:driverlib.a
            -L${COM_TI_MSPM0_SDK_INSTALL_DIR}/source
            -L${PROJECT_ROOT}
            -L${PROJECT_BUILD_DIR}/syscfg
            -march=armv6-m
            -mthumb
            -static
            -Wl,--gc-sections
            -L${CG_TOOL_ROOT}/arm-none-eabi/lib/thumb/v6-m/nofp
            -lgcc
            -lc
            -lm
            --specs=nosys.specs
        "
        sysConfigBuildOptions="
            --output .
            --product ${COM_TI_MSPM0_SDK_INSTALL_DIR}/.metadata/product.json
            --compiler gcc
        "
        sourceLookupPath="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"
        description="Empty example to be used as a starting point for new development">

        <property name="buildProfile" value="release"/>
        <property name="isHybrid" value="true"/>
        <file path="../empty.c" openOnCreation="false" excludeFromBuild="false" action="copy">
        </file>
        <file path="../README.md" openOnCreation="true" excludeFromBuild="false" action="copy">
        </file>
        <file path="../README.html" openOnCreation="false" excludeFromBuild="false" action="copy">
        </file>
        <file path="../empty.syscfg" openOnCreation="true" excludeFromBuild="false" action="copy">
        </file>
    </project>
</projectSpec>