# 系统架构设计文档 - 七路灰度传感器黑白监测系统

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-10
- **架构师**: Bob
- **项目名称**: 七路灰度传感器黑白监测系统
- **目标平台**: MSPM0G3507微控制器 (ARM Cortex-M0+)

## 2. 架构概览

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  主控制逻辑  │  数据输出接口  │  配置管理  │  状态监控      │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)               │
├─────────────────────────────────────────────────────────────┤
│  数据处理模块  │  滤波算法  │  阈值判断  │  状态管理       │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层 (HAL Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  ADC驱动模块  │  GPIO配置  │  中断管理  │  定时器管理      │
├─────────────────────────────────────────────────────────────┤
│                    硬件层 (Hardware Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  MSPM0G3507  │  ADC0/ADC1  │  GPIO端口  │  系统时钟       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心设计原则
- **模块化设计**: 各功能模块独立，接口清晰
- **实时性保证**: 中断驱动的数据采集，确保1ms响应时间
- **资源优化**: 最小化内存和Flash占用
- **可配置性**: 支持运行时参数调整
- **容错性**: 完善的错误检测和恢复机制

## 3. 硬件架构设计

### 3.1 引脚映射与ADC通道分配

#### 3.1.1 H7连接器引脚映射
```c
// H7连接器引脚定义
#define H7_PIN_GND      1   // 地线
#define H7_PIN_5V       2   // 5V电源
#define H7_PIN_B12      3   // 传感器1 -> ADC0_CH0
#define H7_PIN_B17      4   // 传感器2 -> ADC0_CH1  
#define H7_PIN_B04      5   // 传感器3 -> ADC0_CH2
#define H7_PIN_B01      6   // 传感器4 -> ADC0_CH3
#define H7_PIN_A28      7   // 传感器5 -> ADC1_CH0
#define H7_PIN_A31      8   // 传感器6 -> ADC1_CH1
#define H7_PIN_B15      9   // 传感器7 -> ADC1_CH2
```

#### 3.1.2 ADC通道配置策略
- **ADC0**: 负责传感器1-4 (B12, B17, B04, B01)
- **ADC1**: 负责传感器5-7 (A28, A31, B15)
- **采样模式**: 连续转换模式，DMA传输
- **采样频率**: 每通道1kHz，总采样率7kHz
- **分辨率**: 12位 (0-4095)
- **参考电压**: 内部3.3V参考

### 3.2 时钟和电源架构

#### 3.2.1 系统时钟配置
```c
// 系统时钟配置 (基于现有配置)
#define SYSTEM_CLOCK_FREQ   32000000    // 32MHz主时钟
#define ADC_CLOCK_FREQ      4000000     // 4MHz ADC时钟
#define TIMER_CLOCK_FREQ    32000000    // 32MHz定时器时钟
```

#### 3.2.2 电源管理策略
- **工作模式**: SLEEP0低功耗模式
- **功耗预算**: <100mA@3.3V
- **电源监控**: BOR阈值Level 0
- **未使用引脚**: 配置为GPIO输出低电平

## 4. 软件架构设计

### 4.1 模块架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        main.c                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  初始化管理器    │  │  主控制循环      │  │  错误处理    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    sensor_manager.c                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  传感器控制      │  │  数据聚合        │  │  状态管理    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    adc_driver.c                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  ADC配置        │  │  中断处理        │  │  DMA管理     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    data_processor.c                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  滤波算法        │  │  阈值判断        │  │  状态输出    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 核心数据结构设计

#### 4.2.1 传感器数据结构
```c
// 单个传感器数据结构
typedef struct {
    uint16_t raw_value;         // 原始ADC值 (0-4095)
    uint16_t filtered_value;    // 滤波后的值
    uint8_t  state;            // 当前状态 (0=黑色, 1=白色)
    uint8_t  error_flag;       // 错误标志位
    uint32_t last_update_time; // 最后更新时间戳
} sensor_data_t;

// 七路传感器数据集合
typedef struct {
    sensor_data_t sensors[7];   // 七路传感器数据
    uint8_t combined_state;     // 组合状态码 (7位)
    uint32_t timestamp;         // 全局时间戳
    uint8_t system_status;      // 系统状态
} sensor_system_t;
```

#### 4.2.2 配置参数结构
```c
// 系统配置参数
typedef struct {
    uint16_t black_threshold;   // 黑色判断阈值
    uint16_t white_threshold;   // 白色判断阈值
    uint8_t  filter_window;     // 滤波窗口大小
    uint16_t sample_rate;       // 采样频率 (Hz)
    uint8_t  hysteresis_enable; // 迟滞比较使能
    uint16_t hysteresis_margin; // 迟滞边界
} sensor_config_t;
```

### 4.3 关键算法设计

#### 4.3.1 移动平均滤波算法
```c
// 移动平均滤波器结构
typedef struct {
    uint16_t buffer[FILTER_WINDOW_SIZE];
    uint8_t  index;
    uint32_t sum;
    uint8_t  initialized;
} moving_average_filter_t;

// 滤波算法实现
uint16_t apply_moving_average_filter(moving_average_filter_t* filter, uint16_t new_value);
```

#### 4.3.2 自适应阈值判断算法
```c
// 阈值判断状态机
typedef enum {
    THRESHOLD_STATE_UNKNOWN = 0,
    THRESHOLD_STATE_BLACK   = 1,
    THRESHOLD_STATE_WHITE   = 2,
    THRESHOLD_STATE_GRAY    = 3
} threshold_state_t;

// 带迟滞的阈值判断
uint8_t adaptive_threshold_judge(uint16_t value, uint8_t previous_state, sensor_config_t* config);
```

## 5. 中断和实时性设计

### 5.1 中断优先级配置
```c
// 中断优先级定义 (数值越小优先级越高)
#define ADC0_IRQ_PRIORITY   0   // 最高优先级
#define ADC1_IRQ_PRIORITY   0   // 最高优先级  
#define TIMER_IRQ_PRIORITY  1   // 中等优先级
#define UART_IRQ_PRIORITY   2   // 低优先级
```

### 5.2 实时性保证机制

#### 5.2.1 ADC中断处理流程
```
ADC转换完成中断 → 读取ADC值 → 存储到缓冲区 → 设置数据就绪标志 → 退出中断
                                                    ↓
主循环检测标志 → 调用数据处理 → 滤波和阈值判断 → 更新输出状态
```

#### 5.2.2 时间预算分析
- **ADC中断处理**: <10μs
- **数据处理**: <100μs  
- **状态更新**: <50μs
- **总响应时间**: <200μs (远小于1ms要求)

## 6. 内存和资源管理

### 6.1 内存使用预算

#### 6.1.1 Flash存储分配
```
- 程序代码:        ~6KB
- 常量数据:        ~1KB  
- 系统库:          ~1KB
- 总计:            ~8KB (符合<8KB要求)
```

#### 6.1.2 RAM使用分配
```
- 全局变量:        ~512B
- 栈空间:          ~1KB
- 堆空间:          ~256B
- 缓冲区:          ~256B
- 总计:            ~2KB (符合<2KB要求)
```

### 6.2 资源优化策略
- **代码优化**: 使用-O2编译优化
- **数据对齐**: 结构体4字节对齐
- **常量优化**: 使用const修饰符
- **内联函数**: 关键路径函数内联

## 7. 错误处理和容错设计

### 7.1 错误检测机制

#### 7.1.1 硬件错误检测
```c
// 错误类型定义
typedef enum {
    ERROR_NONE          = 0x00,
    ERROR_ADC_TIMEOUT   = 0x01,
    ERROR_ADC_OVERRUN   = 0x02,
    ERROR_SENSOR_FAULT  = 0x04,
    ERROR_POWER_FAULT   = 0x08,
    ERROR_SYSTEM_FAULT  = 0x10
} error_code_t;
```

#### 7.1.2 软件错误检测
- **看门狗定时器**: 防止程序死锁
- **栈溢出检测**: 监控栈使用情况
- **数据完整性**: CRC校验关键数据

### 7.2 容错和恢复策略

#### 7.2.1 传感器故障处理
```c
// 传感器故障检测和处理
bool detect_sensor_fault(uint8_t sensor_id, uint16_t adc_value) {
    if (adc_value < ADC_MIN_VALID || adc_value > ADC_MAX_VALID) {
        set_sensor_error_flag(sensor_id);
        return true;
    }
    return false;
}
```

#### 7.2.2 系统恢复机制
- **软复位**: 检测到严重错误时执行软复位
- **参数恢复**: 恢复默认配置参数
- **状态重置**: 清除错误状态，重新初始化

## 8. 接口设计规范

### 8.1 模块间接口定义

#### 8.1.1 ADC驱动接口
```c
// ADC驱动接口函数
typedef struct {
    bool (*init)(void);
    bool (*start_conversion)(void);
    bool (*stop_conversion)(void);
    uint16_t (*read_channel)(uint8_t channel);
    bool (*is_conversion_complete)(void);
    void (*register_callback)(void (*callback)(uint8_t channel, uint16_t value));
} adc_driver_interface_t;
```

#### 8.1.2 数据处理接口
```c
// 数据处理接口函数
typedef struct {
    void (*init)(sensor_config_t* config);
    void (*process_sample)(uint8_t sensor_id, uint16_t raw_value);
    uint8_t (*get_sensor_state)(uint8_t sensor_id);
    uint8_t (*get_combined_state)(void);
    bool (*update_config)(sensor_config_t* new_config);
} data_processor_interface_t;
```

### 8.2 外部接口设计

#### 8.2.1 状态查询接口
```c
// 外部状态查询API
uint8_t get_sensor_states(void);           // 获取7位状态码
uint16_t get_raw_value(uint8_t sensor_id); // 获取原始ADC值
uint8_t get_system_status(void);           // 获取系统状态
uint32_t get_last_update_time(void);       // 获取最后更新时间
```

#### 8.2.2 配置管理接口
```c
// 配置管理API
bool set_threshold(uint16_t black_th, uint16_t white_th);
bool set_filter_window(uint8_t window_size);
bool enable_hysteresis(bool enable, uint16_t margin);
bool save_config_to_flash(void);
bool load_config_from_flash(void);
```

## 9. 性能优化策略

### 9.1 代码优化
- **编译器优化**: 使用-O2优化级别
- **循环展开**: 关键循环手动展开
- **查表法**: 复杂计算使用预计算表
- **位操作**: 使用位操作替代除法和模运算

### 9.2 算法优化
- **整数运算**: 避免浮点运算
- **移位操作**: 使用移位替代乘除法
- **条件分支**: 减少条件分支预测失败
- **内存访问**: 优化数据结构布局

### 9.3 实时性优化
- **中断延迟**: 最小化中断处理时间
- **任务调度**: 优化主循环任务顺序
- **缓存友好**: 提高数据局部性
- **预取优化**: 减少内存访问延迟

## 10. 测试和验证策略

### 10.1 单元测试设计
- **ADC驱动测试**: 验证各通道采样精度
- **滤波算法测试**: 验证滤波效果和性能
- **阈值判断测试**: 验证判断逻辑正确性
- **错误处理测试**: 验证异常情况处理

### 10.2 集成测试设计
- **七路协同测试**: 验证多通道同步工作
- **实时性测试**: 验证响应时间要求
- **稳定性测试**: 长时间运行测试
- **边界条件测试**: 极限条件下的行为验证

### 10.3 性能基准测试
- **采样频率测试**: 验证1kHz采样率
- **功耗测试**: 验证功耗<100mA要求
- **内存使用测试**: 验证Flash和RAM占用
- **准确率测试**: 验证99%判断准确率

## 11. 技术选型决策

### 11.1 ADC配置选择
**决策**: 使用ADC0和ADC1双ADC配置
**理由**:
- 分散负载，提高采样效率
- 降低通道间串扰
- 支持并行采样，提升实时性

### 11.2 数据传输方式
**决策**: 中断驱动 + 循环缓冲区
**理由**:
- 实时性好，响应延迟低
- CPU占用率低
- 数据不丢失，可靠性高

### 11.3 滤波算法选择
**决策**: 移动平均滤波
**理由**:
- 计算简单，实时性好
- 内存占用小
- 对高频噪声抑制效果好

## 12. 实现指导原则

### 12.1 编码规范
- 函数名使用小写+下划线命名
- 宏定义使用大写+下划线命名
- 结构体使用_t后缀
- 全局变量使用g_前缀

### 12.2 注释规范
- 所有函数必须有功能说明注释
- 复杂算法必须有实现原理注释
- 关键参数必须有取值范围注释
- 错误处理必须有处理策略注释

### 12.3 错误处理规范
- 所有函数返回值必须检查
- 关键操作必须有超时保护
- 异常情况必须记录错误码
- 系统故障必须有恢复机制

---

**架构设计状态**: ✅ 已完成
**技术选型决策**: ✅ 已完成
**实现指导**: ✅ 已完成
**下一步**: 转交Alex进行代码实现
