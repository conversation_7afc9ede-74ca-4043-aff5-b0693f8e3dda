/*
 * 七路灰度传感器黑白监测系统
 * 基于MSPM0G3507微控制器
 *
 * 功能: 实时监测七路灰度传感器，判断黑色与白色
 * 硬件: H7连接器 - Pin3:B12, Pin4:B17, Pin5:B04, Pin6:B01, Pin7:A28, Pin8:A31, Pin9:B15
 *
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

// ==================== 系统配置参数 ====================
#define NUM_SENSORS             7       // 传感器数量
#define FILTER_WINDOW_SIZE      8       // 滤波窗口大小
#define BLACK_THRESHOLD         1500    // 黑色判断阈值 (0-4095)
#define WHITE_THRESHOLD         2500    // 白色判断阈值 (0-4095)
#define HYSTERESIS_MARGIN       100     // 迟滞边界
#define ADC_MAX_VALUE           4095    // 12位ADC最大值
#define SAMPLE_RATE_HZ          1000    // 采样频率1kHz

// ==================== 硬件引脚映射 ====================
// H7连接器引脚到GPIO映射
#define SENSOR1_PIN             DL_GPIO_PIN_12  // B12 -> ADC0_CH0
#define SENSOR2_PIN             DL_GPIO_PIN_17  // B17 -> ADC0_CH1
#define SENSOR3_PIN             DL_GPIO_PIN_4   // B04 -> ADC0_CH2
#define SENSOR4_PIN             DL_GPIO_PIN_1   // B01 -> ADC0_CH3
#define SENSOR5_PIN             DL_GPIO_PIN_28  // A28 -> ADC1_CH0
#define SENSOR6_PIN             DL_GPIO_PIN_31  // A31 -> ADC1_CH1
#define SENSOR7_PIN             DL_GPIO_PIN_15  // B15 -> ADC1_CH2

// ==================== 数据结构定义 ====================
// 单个传感器数据结构
typedef struct {
    uint16_t raw_value;         // 原始ADC值
    uint16_t filtered_value;    // 滤波后的值
    uint8_t  state;            // 当前状态 (0=黑色, 1=白色)
    uint8_t  error_flag;       // 错误标志位
    uint32_t last_update_time; // 最后更新时间戳
} sensor_data_t;

// 移动平均滤波器结构
typedef struct {
    uint16_t buffer[FILTER_WINDOW_SIZE];
    uint8_t  index;
    uint32_t sum;
    bool     initialized;
} moving_average_filter_t;

// 系统配置结构
typedef struct {
    uint16_t black_threshold;   // 黑色阈值
    uint16_t white_threshold;   // 白色阈值
    uint8_t  filter_window;     // 滤波窗口
    bool     hysteresis_enable; // 迟滞使能
    uint16_t hysteresis_margin; // 迟滞边界
} sensor_config_t;

// ==================== 全局变量 ====================
static sensor_data_t g_sensors[NUM_SENSORS];                    // 传感器数据数组
static moving_average_filter_t g_filters[NUM_SENSORS];          // 滤波器数组
static sensor_config_t g_config;                               // 系统配置
static volatile bool g_adc_conversion_complete = false;         // ADC转换完成标志
static volatile uint16_t g_adc_results[NUM_SENSORS];           // ADC结果缓冲区
static uint32_t g_system_tick = 0;                             // 系统时钟计数

// ==================== 函数声明 ====================
void system_init(void);                                        // 系统初始化
void adc_init(void);                                           // ADC初始化
void gpio_init(void);                                          // GPIO初始化
void timer_init(void);                                         // 定时器初始化
void sensors_init(void);                                       // 传感器初始化
uint16_t apply_moving_average_filter(moving_average_filter_t* filter, uint16_t new_value); // 移动平均滤波
uint8_t adaptive_threshold_judge(uint16_t value, uint8_t previous_state); // 自适应阈值判断
void process_sensor_data(void);                                // 处理传感器数据
uint8_t get_combined_sensor_states(void);                      // 获取组合状态
void handle_sensor_error(uint8_t sensor_id);                   // 处理传感器错误
void ADC0_IRQHandler(void);                                    // ADC0中断处理
void ADC1_IRQHandler(void);                                    // ADC1中断处理
void TIMG0_IRQHandler(void);                                   // 定时器中断处理

// ==================== 主函数 ====================
int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    system_init();

    // 主循环
    while (1) {
        // 检查ADC转换完成
        if (g_adc_conversion_complete) {
            g_adc_conversion_complete = false;
            process_sensor_data();  // 处理传感器数据
        }

        // 可选: 添加低功耗模式
        __WFI();  // 等待中断
    }
}

// ==================== 系统初始化函数 ====================
void system_init(void)
{
    // 初始化系统配置
    g_config.black_threshold = BLACK_THRESHOLD;
    g_config.white_threshold = WHITE_THRESHOLD;
    g_config.filter_window = FILTER_WINDOW_SIZE;
    g_config.hysteresis_enable = true;
    g_config.hysteresis_margin = HYSTERESIS_MARGIN;

    // 初始化各个模块
    gpio_init();        // GPIO配置
    adc_init();         // ADC配置
    timer_init();       // 定时器配置
    sensors_init();     // 传感器数据初始化

    // 启用全局中断
    __enable_irq();

    // 启动ADC转换
    DL_ADC12_startConversion(ADC0);
    DL_ADC12_startConversion(ADC1);
}

// ==================== GPIO初始化 ====================
void gpio_init(void)
{
    // 配置传感器引脚为模拟输入
    // GPIOB引脚配置 (B12, B17, B04, B01, B15)
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_B12);  // 传感器1
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_B17);  // 传感器2
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_B04);  // 传感器3
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_B01);  // 传感器4
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_B15);  // 传感器7

    // GPIOA引脚配置 (A28, A31)
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_A28);  // 传感器5
    DL_GPIO_initPeripheralAnalogFunction(DL_GPIO_IOMUX_A31);  // 传感器6
}

// ==================== ADC初始化 ====================
void adc_init(void)
{
    // ADC0配置 (传感器1-4: B12, B17, B04, B01)
    DL_ADC12_reset(ADC0);
    DL_ADC12_enablePower(ADC0);

    // ADC0基本配置 - 简化版本，避免使用不存在的配置结构
    DL_ADC12_setClockFreqRange(ADC0, DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32);
    DL_ADC12_initSeqSample(ADC0, DL_ADC12_REPEAT_MODE_ENABLED,
        DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);

    // 配置ADC0的4个通道 (传感器1-4)
    DL_ADC12_configConversionMem(ADC0, DL_ADC12_MEM_IDX_0,
        DL_ADC12_INPUT_CHAN_0, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    DL_ADC12_configConversionMem(ADC0, DL_ADC12_MEM_IDX_1,
        DL_ADC12_INPUT_CHAN_1, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    DL_ADC12_configConversionMem(ADC0, DL_ADC12_MEM_IDX_2,
        DL_ADC12_INPUT_CHAN_2, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    DL_ADC12_configConversionMem(ADC0, DL_ADC12_MEM_IDX_3,
        DL_ADC12_INPUT_CHAN_3, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    // 配置采样时间
    DL_ADC12_setSampleTime0(ADC0, 25);

    // 启用ADC0中断
    DL_ADC12_enableInterrupt(ADC0, DL_ADC12_INTERRUPT_MEM3_RESULT_LOADED);

    // ADC1配置 (传感器5-7: A28, A31, B15)
    DL_ADC12_reset(ADC1);
    DL_ADC12_enablePower(ADC1);

    // ADC1基本配置
    DL_ADC12_setClockFreqRange(ADC1, DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32);
    DL_ADC12_initSeqSample(ADC1, DL_ADC12_REPEAT_MODE_ENABLED,
        DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);

    DL_ADC12_configConversionMem(ADC1, DL_ADC12_MEM_IDX_0,
        DL_ADC12_INPUT_CHAN_0, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    DL_ADC12_configConversionMem(ADC1, DL_ADC12_MEM_IDX_1,
        DL_ADC12_INPUT_CHAN_1, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    DL_ADC12_configConversionMem(ADC1, DL_ADC12_MEM_IDX_2,
        DL_ADC12_INPUT_CHAN_2, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

    // 配置采样时间
    DL_ADC12_setSampleTime0(ADC1, 25);

    // 启用ADC1中断
    DL_ADC12_enableInterrupt(ADC1, DL_ADC12_INTERRUPT_MEM2_RESULT_LOADED);

    // 启用ADC
    DL_ADC12_enableConversions(ADC0);
    DL_ADC12_enableConversions(ADC1);
}

// ==================== 定时器初始化 ====================
void timer_init(void)
{
    // 配置定时器用于定期触发ADC转换 (1kHz)
    DL_TimerG_reset(TIMG0);
    DL_TimerG_enablePower(TIMG0);

    // 配置定时器为周期模式，1kHz频率 (32MHz/32000 = 1kHz)
    DL_TimerG_setClockConfig(TIMG0, DL_TIMER_CLOCK_BUSCLK, DL_TIMER_CLOCK_DIVIDE_1);
    DL_TimerG_initPeriodicMode(TIMG0, 32000);  // 32MHz/32000 = 1kHz

    // 启用定时器中断
    DL_TimerG_enableInterrupt(TIMG0, DL_TIMER_INTERRUPT_ZERO_EVENT);

    // 启动定时器
    DL_TimerG_startCounter(TIMG0);
}

// ==================== 传感器数据初始化 ====================
void sensors_init(void)
{
    for (int i = 0; i < NUM_SENSORS; i++) {
        // 初始化传感器数据
        g_sensors[i].raw_value = 0;
        g_sensors[i].filtered_value = 0;
        g_sensors[i].state = 0;  // 默认黑色
        g_sensors[i].error_flag = 0;
        g_sensors[i].last_update_time = 0;

        // 初始化滤波器
        g_filters[i].index = 0;
        g_filters[i].sum = 0;
        g_filters[i].initialized = false;
        for (int j = 0; j < FILTER_WINDOW_SIZE; j++) {
            g_filters[i].buffer[j] = 0;
        }
    }
}

// ==================== 移动平均滤波器 ====================
uint16_t apply_moving_average_filter(moving_average_filter_t* filter, uint16_t new_value)
{
    if (!filter->initialized) {
        // 首次初始化，填充所有缓冲区
        for (int i = 0; i < FILTER_WINDOW_SIZE; i++) {
            filter->buffer[i] = new_value;
        }
        filter->sum = new_value * FILTER_WINDOW_SIZE;
        filter->initialized = true;
        return new_value;
    }

    // 移除最旧的值，添加新值
    filter->sum -= filter->buffer[filter->index];
    filter->buffer[filter->index] = new_value;
    filter->sum += new_value;

    // 更新索引
    filter->index = (filter->index + 1) % FILTER_WINDOW_SIZE;

    // 返回平均值
    return (uint16_t)(filter->sum / FILTER_WINDOW_SIZE);
}

// ==================== 自适应阈值判断 ====================
uint8_t adaptive_threshold_judge(uint16_t value, uint8_t previous_state)
{
    if (g_config.hysteresis_enable) {
        // 带迟滞的阈值判断
        if (previous_state == 0) {  // 当前是黑色
            // 需要超过白色阈值+迟滞边界才变为白色
            if (value > (g_config.white_threshold + g_config.hysteresis_margin)) {
                return 1;  // 白色
            }
        } else {  // 当前是白色
            // 需要低于黑色阈值-迟滞边界才变为黑色
            if (value < (g_config.black_threshold - g_config.hysteresis_margin)) {
                return 0;  // 黑色
            }
        }
        return previous_state;  // 保持当前状态
    } else {
        // 简单阈值判断
        if (value < g_config.black_threshold) {
            return 0;  // 黑色
        } else if (value > g_config.white_threshold) {
            return 1;  // 白色
        } else {
            return previous_state;  // 灰色区域，保持上一状态
        }
    }
}

// ==================== 处理传感器数据 ====================
void process_sensor_data(void)
{
    g_system_tick++;  // 更新系统时钟

    for (int i = 0; i < NUM_SENSORS; i++) {
        // 获取原始ADC值
        uint16_t raw_value = g_adc_results[i];
        g_sensors[i].raw_value = raw_value;

        // 检查传感器故障
        if (raw_value > ADC_MAX_VALUE || raw_value == 0) {
            handle_sensor_error(i);
            continue;
        }

        // 应用移动平均滤波
        uint16_t filtered_value = apply_moving_average_filter(&g_filters[i], raw_value);
        g_sensors[i].filtered_value = filtered_value;

        // 自适应阈值判断
        uint8_t new_state = adaptive_threshold_judge(filtered_value, g_sensors[i].state);
        g_sensors[i].state = new_state;

        // 更新时间戳
        g_sensors[i].last_update_time = g_system_tick;

        // 清除错误标志
        g_sensors[i].error_flag = 0;
    }
}

// ==================== 获取组合传感器状态 ====================
uint8_t get_combined_sensor_states(void)
{
    uint8_t combined_state = 0;

    for (int i = 0; i < NUM_SENSORS; i++) {
        if (g_sensors[i].state) {
            combined_state |= (1 << i);  // 设置对应位
        }
    }

    return combined_state;
}

// ==================== 处理传感器错误 ====================
void handle_sensor_error(uint8_t sensor_id)
{
    if (sensor_id < NUM_SENSORS) {
        g_sensors[sensor_id].error_flag = 1;
        // 保持上一个有效状态，不更新
        // 可以在这里添加错误恢复逻辑
    }
}

// ==================== 中断处理函数 ====================

// ADC0中断处理 (传感器1-4)
void ADC0_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC0)) {
        case DL_ADC12_IIDX_MEM3_RESULT_LOADED:
            // 读取四个通道的ADC结果
            g_adc_results[0] = DL_ADC12_getMemResult(ADC0, DL_ADC12_MEM_IDX_0);  // 传感器1
            g_adc_results[1] = DL_ADC12_getMemResult(ADC0, DL_ADC12_MEM_IDX_1);  // 传感器2
            g_adc_results[2] = DL_ADC12_getMemResult(ADC0, DL_ADC12_MEM_IDX_2);  // 传感器3
            g_adc_results[3] = DL_ADC12_getMemResult(ADC0, DL_ADC12_MEM_IDX_3);  // 传感器4
            break;
        default:
            break;
    }
}

// ADC1中断处理 (传感器5-7)
void ADC1_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC1)) {
        case DL_ADC12_IIDX_MEM2_RESULT_LOADED:
            // 读取三个通道的ADC结果
            g_adc_results[4] = DL_ADC12_getMemResult(ADC1, DL_ADC12_MEM_IDX_0);  // 传感器5
            g_adc_results[5] = DL_ADC12_getMemResult(ADC1, DL_ADC12_MEM_IDX_1);  // 传感器6
            g_adc_results[6] = DL_ADC12_getMemResult(ADC1, DL_ADC12_MEM_IDX_2);  // 传感器7

            // 设置转换完成标志
            g_adc_conversion_complete = true;
            break;
        default:
            break;
    }
}

// 定时器中断处理 (1kHz触发ADC转换)
void TIMG0_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMG0)) {
        case DL_TIMER_IIDX_ZERO:
            // 触发新的ADC转换
            DL_ADC12_startConversion(ADC0);
            DL_ADC12_startConversion(ADC1);
            break;
        default:
            break;
    }
}

// ==================== 外部接口函数 ====================

// 获取传感器状态 (返回7位状态码)
uint8_t get_sensor_states(void)
{
    return get_combined_sensor_states();
}

// 获取单个传感器原始值
uint16_t get_raw_value(uint8_t sensor_id)
{
    if (sensor_id < NUM_SENSORS) {
        return g_sensors[sensor_id].raw_value;
    }
    return 0;
}

// 获取单个传感器滤波值
uint16_t get_filtered_value(uint8_t sensor_id)
{
    if (sensor_id < NUM_SENSORS) {
        return g_sensors[sensor_id].filtered_value;
    }
    return 0;
}

// 获取单个传感器状态
uint8_t get_sensor_state(uint8_t sensor_id)
{
    if (sensor_id < NUM_SENSORS) {
        return g_sensors[sensor_id].state;
    }
    return 0;
}

// 检查传感器错误
bool is_sensor_error(uint8_t sensor_id)
{
    if (sensor_id < NUM_SENSORS) {
        return g_sensors[sensor_id].error_flag != 0;
    }
    return true;
}

// 获取系统时钟
uint32_t get_system_tick(void)
{
    return g_system_tick;
}

// 设置阈值
bool set_threshold(uint16_t black_th, uint16_t white_th)
{
    if (black_th < white_th && white_th <= ADC_MAX_VALUE) {
        g_config.black_threshold = black_th;
        g_config.white_threshold = white_th;
        return true;
    }
    return false;
}

// 启用/禁用迟滞
void set_hysteresis(bool enable, uint16_t margin)
{
    g_config.hysteresis_enable = enable;
    g_config.hysteresis_margin = margin;
}

/*
 * 使用说明:
 * 1. 系统启动后自动开始1kHz采样
 * 2. 调用get_sensor_states()获取7位状态码 (bit0-bit6对应传感器1-7)
 * 3. 调用get_raw_value(sensor_id)获取原始ADC值
 * 4. 调用get_sensor_state(sensor_id)获取单个传感器状态 (0=黑色, 1=白色)
 * 5. 调用is_sensor_error(sensor_id)检查传感器是否故障
 *
 * 硬件连接:
 * H7-Pin3(B12) -> 传感器1
 * H7-Pin4(B17) -> 传感器2
 * H7-Pin5(B04) -> 传感器3
 * H7-Pin6(B01) -> 传感器4
 * H7-Pin7(A28) -> 传感器5
 * H7-Pin8(A31) -> 传感器6
 * H7-Pin9(B15) -> 传感器7
 */
